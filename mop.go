package mop

import (
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"log"
	"strings"
)

// HandlerFunc 给mop定义一个mqtt的handler
type HandlerFunc func(c *Context)

// Engine 定义一个mop的核心结构体，保存了topic分组、mqtt客户端实例、配置等信息
type Engine struct {
	*TopicGroup
	client mqtt.Client
	config *Config
	topic  *topic
	groups []*TopicGroup // store all groups
}

// New 创建一个 mop.Engine 的构造体
func New(config *Config) *Engine {
	engine := &Engine{topic: newTopic(), config: config}
	engine.TopicGroup = &TopicGroup{engine: engine}
	engine.groups = []*TopicGroup{engine.TopicGroup}
	return engine
}

// Run 创建mqtt客户端并连接broker
func (engine *Engine) Run() {
	if engine.config == nil {
		log.Fatalln("config is nil")
	}
	// 创建客户端
	client := mqtt.NewClient(engine.config.newOpts(engine))

	// 尝试连接
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		log.Fatalf("connect failed: %v\n", token.Error())
	}

	if !client.IsConnected() {
		log.Fatalf("broker %s is not connected\n", engine.config.Broker)
	}
}

// 中间件
func (engine *Engine) middleware(topic string) []HandlerFunc {
	var middlewares []HandlerFunc
	for _, group := range engine.groups {
		if strings.HasPrefix(topic, group.prefix) {
			middlewares = append(middlewares, group.middlewares...)
		}
	}
	return middlewares
}

// 连接器
func (engine *Engine) onConnectHandler(client mqtt.Client) {
	engine.client = client
	topics := engine.topic.getTopics()
	for _, t := range topics {
		client.Subscribe(t.topic, t.qos, func(client mqtt.Client, message mqtt.Message) {
			c := newContext(message.Topic(), client, message)
			c.handlers = engine.middleware(message.Topic())
			engine.topic.handle(c)
		})
	}
	log.Printf("mqtt client connect to %s success\n", engine.config.Broker)
}
