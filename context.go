package mop

import (
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

const abortIndex = 65536

// Context 上下文
type Context struct {
	// mqtt
	Client  mqtt.Client
	Message mqtt.Message
	// topic
	Topic  string
	Params map[string]string
	// middleware
	handlers []HandlerFunc
	index    int
}

// 创建新的上下文
func newContext(topic string, client mqtt.Client, message mqtt.Message) *Context {
	c := &Context{
		Client:  client,
		Message: message,
		Topic:   topic,
		index:   -1,
	}
	return c
}

// Param 当前主题的可变参数
func (c *Context) Param(key string) string {
	value, _ := c.Params[key]
	return value
}

// Next 执行下一个handler
func (c *Context) Next() {
	c.index++
	for c.index < len(c.handlers) {
		if c.handlers[c.index] == nil {
			continue
		}
		c.handlers[c.index](c)
		c.index++
	}
}

// Abort 忽略当前handler
func (c *Context) Abort() {
	c.index = abortIndex
}
