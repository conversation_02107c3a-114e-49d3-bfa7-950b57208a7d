# mop
 
mop是一个mqtt订阅主题管理框架，主题统一路由式管理

### 简单示例

```go
package main

import (
	"log"
	"mop"
)

func main() {
	config := &mop.Config{
		ClientID:           "mop123",
		Broker:             "mqtts://test.maitian-yun.com:30082",
		Username:           "mop123",
		Password:           "mop123",
		CertFile:           "./cert/server.pem",
		KeyFile:            "./cert/server.key",
		InsecureSkipVerify: true,
	}
	topic := mop.New(config)
	topic.Subscribe("/#", 0x00, func(c *mop.Context) {
		log.Println(string(c.Message.Payload()))
	})
	topic.Run()
}

```
