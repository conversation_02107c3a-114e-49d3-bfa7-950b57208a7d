package mop

import mqtt "github.com/eclipse/paho.mqtt.golang"

// TopicGroup topic分组管理
type TopicGroup struct {
	prefix      string
	middlewares []HandlerFunc
	parent      *TopicGroup
	engine      *Engine
}

// Group 定义一个 TopicGroup
// 所有的group共享一个 Engine 实例
func (group *TopicGroup) Group(prefix string) *TopicGroup {
	engine := group.engine
	newGroup := &TopicGroup{
		prefix: prefix,
		parent: group,
		engine: engine,
	}
	engine.groups = append(engine.groups, newGroup)
	return newGroup
}

// Use 添加中间件到 TopicGroup
func (group *TopicGroup) Use(middlewares ...HandlerFunc) {
	group.middlewares = append(group.middlewares, middlewares...)
}

// 添加topic
func (group *TopicGroup) addTopic(topic string, qos byte, handler HandlerFunc) {
	pattern := group.prefix + topic
	group.engine.topic.addTopic(pattern, qos, handler)
}

// Subscribe 订阅topic
func (group *TopicGroup) Subscribe(topic string, qos byte, handler <PERSON><PERSON><PERSON><PERSON><PERSON>) {
	group.addTopic(topic, qos, handler)
}

// Publish 发布topic
func (group *TopicGroup) Publish(topic string, qos byte, retained bool, payload interface{}) {
	group.engine.client.Publish(topic, qos, retained, payload)
}

// Client 返回mqtt客户端实例
func (group *TopicGroup) Client() mqtt.Client {
	return group.engine.client
}
