package mop

import (
	"crypto/tls"
	"crypto/x509"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"log"
	"os"
	"time"
)

// Config 客户端配置
type Config struct {
	ClientID           string // mqtt客户端id
	Broker             string // mqtt连接地址
	Username           string // 用户名
	Password           string // 密码
	RootCA             string // ca证书
	CertFile           string // 证书
	KeyFile            string // 密钥
	InsecureSkipVerify bool   // 跳过认证
}

// newOpts 客户端配置
func (c *Config) newOpts(engine *Engine) *mqtt.ClientOptions {
	opts := mqtt.NewClientOptions()
	opts.SetClientID(c.ClientID)             // 客户端 ID
	opts.AddBroker(c.Broker)                 // MQTT 代理地址
	opts.SetTLSConfig(c.tlsConfig())         // tls配置
	opts.SetUsername(c.Username)             // 如果需要身份验证，设置用户名
	opts.SetPassword(c.Password)             // 设置密码
	opts.SetConnectTimeout(15 * time.Second) // 连接超时时间
	opts.SetKeepAlive(30 * time.Second)
	opts.SetCleanSession(true)
	opts.SetMaxReconnectInterval(10 * time.Second)
	opts.SetProtocolVersion(4)
	opts.SetPingTimeout(300 * time.Hour)
	opts.SetOnConnectHandler(engine.onConnectHandler)
	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		log.Printf("disconnect on %s lost handler %v\n", c.Broker, err)
	})
	return opts
}

// tlsConfig tls证书配置
func (c *Config) tlsConfig() *tls.Config {
	var pool *x509.CertPool
	if c.RootCA != "" {
		// open ca crt
		pem, err := os.ReadFile(c.RootCA)
		if err != nil {
			log.Fatalln("open ca cert failed:", err)
		}
		// 创建根证书池并添加ca证书
		pool = x509.NewCertPool()
		ok := pool.AppendCertsFromPEM(pem)
		if !ok {
			log.Fatalln("append certs from pem failed")
		}
	}

	if c.CertFile != "" && c.KeyFile != "" {
		// load client crt file and client key file
		clientCert, err := tls.LoadX509KeyPair(c.CertFile, c.KeyFile)
		if err != nil {
			log.Fatalln("load key pair failed:", err)
		}
		return &tls.Config{
			RootCAs:            pool,
			Certificates:       []tls.Certificate{clientCert},
			InsecureSkipVerify: c.InsecureSkipVerify, // 跳过证书
		}
	}

	return &tls.Config{}
}
