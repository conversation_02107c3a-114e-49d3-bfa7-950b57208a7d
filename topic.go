package mop

import (
	"log"
	"strings"
)

// 保存所有 topic 的节点、中间件
type topic struct {
	root     *node
	qos      map[string]byte
	handlers map[string]HandlerFunc
}

// 创建一个topic节点
func newTopic() *topic {
	return &topic{
		root:     &node{},
		qos:      make(map[string]byte),
		handlers: make(map[string]HandlerFunc),
	}
}

// 一个topic只能有一个#通配符
func parsePattern(pattern string) []string {
	vs := strings.Split(pattern, "/")

	parts := make([]string, 0)
	for _, item := range vs {
		if item != "" {
			parts = append(parts, item)
			if item[0] == '#' {
				break
			}
		}
	}
	return parts
}

// 添加topic
func (t *topic) addTopic(pattern string, qos byte, handler <PERSON><PERSON>Func) {
	parts := parsePattern(pattern)
	if t.root == nil {
		t.root = &node{}
	}
	t.root.insert(pattern, parts, 0)
	t.handlers[pattern] = handler
	t.qos[pattern] = qos
}

// 根据topic获取存储的节点以及topic的参数
func (t *topic) getTopic(topic string) (*node, map[string]string) {
	searchParts := parsePattern(topic)
	params := make(map[string]string)
	if t.root == nil {
		return nil, nil
	}

	n := t.root.search(searchParts, 0)
	if n != nil {
		parts := parsePattern(n.pattern)
		for index, part := range parts {
			if part[0] == ':' {
				params[part[1:]] = searchParts[index]
			}
			if part[0] == '#' && len(part) > 1 {
				params[part[1:]] = strings.Join(searchParts[index:], "/")
				break
			}
		}
		return n, params
	}

	return nil, nil
}

// 订阅的topic
type subscribe struct {
	qos   byte
	topic string
}

// 获取所有订阅的主题
func (t *topic) getTopics() []*subscribe {
	if t.root == nil {
		return nil
	}
	nodes := make([]*node, 0)
	t.root.travel(&nodes)

	var topics []*subscribe
	for _, n := range nodes {
		qos, _ := t.qos[n.pattern]
		parts := parsePattern(n.pattern)
		pattern := t.root.convertPattern(parts)
		topics = append(topics, &subscribe{qos: qos, topic: pattern})
	}

	return topics
}

// 获取topic并执行
func (t *topic) handle(c *Context) {
	n, params := t.getTopic(c.Topic)
	if n != nil {
		c.Params = params
		t.handlers[n.pattern](c)
	} else {
		log.Printf("topic not found: %s\n", c.Topic)
	}
	c.Next()
}
