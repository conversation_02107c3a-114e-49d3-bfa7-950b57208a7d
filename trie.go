package mop

import (
	"fmt"
	"strings"
)

type node struct {
	pattern  string  // 待匹配topic，例如 /device/:deviceID
	part     string  // topic中的一部分，例如 :deviceID
	children []*node // 子节点，例如 [doc, tutorial, intro]
	isWild   bool    // 是否精确匹配，part 含有 : 或 # 或 + 时为true
}

// 判断part是否是通配符
func isWildMatch(part string) bool {
	return part[0] == ':' || part[0] == '+' || part[0] == '#'
}

func (n *node) String() string {
	return fmt.Sprintf("node{pattern=%s, part=%s, isWild=%t}", n.pattern, n.part, n.isWild)
}

// 插入路径
func (n *node) insert(pattern string, parts []string, height int) {
	// 如果已经处理完所有的 parts，将 pattern 存储在当前节点
	if len(parts) == height {
		// 检查是否存在重复 pattern，如果重复则panic
		if n.pattern == pattern {
			panic(fmt.Sprintf("duplicate pattern: %s", pattern))
		}
		n.pattern = pattern
		return
	}

	part := parts[height]
	child := n.matchChild(part)

	// 如果没有匹配到子节点，则创建一个新的子节点
	if child == nil {
		child = &node{part: part, isWild: isWildMatch(part)}
		n.children = append(n.children, child)
	}

	// 递归插入下一个部分
	child.insert(pattern, parts, height+1)
}

// 查找
func (n *node) search(parts []string, height int) *node {
	if len(parts) == height || strings.HasPrefix(n.part, "#") {
		if n.pattern == "" {
			return nil
		}
		return n
	}

	part := parts[height]
	children := n.matchChildren(part)

	for _, child := range children {
		result := child.search(parts, height+1)
		if result != nil {
			return result
		}
	}

	return nil
}

func (n *node) travel(list *[]*node) {
	if n.pattern != "" {
		*list = append(*list, n)
	}
	for _, child := range n.children {
		child.travel(list)
	}
}

// 转换实际主题为存储的主题格式
func (n *node) convertPattern(parts []string) string {
	node := n
	path := make([]string, len(parts)+1)

	for i, part := range parts {
		child := node.matchChild(part)
		if child == nil {
			break
		}
		if part[0] == ':' {
			path[i+1] = "+"
		} else {
			path[i+1] = child.part
		}
		node = child
	}

	return strings.Join(path, "/")
}

// 第一个匹配成功的节点，用于插入
func (n *node) matchChild(part string) *node {
	for _, child := range n.children {
		if child.part == part || child.isWild {
			return child
		}
	}
	return nil
}

// 所有匹配成功的节点，用于查找
func (n *node) matchChildren(part string) []*node {
	nodes := make([]*node, 0)
	for _, child := range n.children {
		if child.part == part || child.isWild {
			nodes = append(nodes, child)
		}
	}
	return nodes
}
